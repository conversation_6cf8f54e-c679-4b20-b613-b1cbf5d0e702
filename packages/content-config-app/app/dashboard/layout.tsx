"use client";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

const queryClient = new QueryClient();

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <QueryClientProvider client={queryClient}>
      <div className="w-full min-h-screen bg-background">
        <main className="w-full p-12 py-4 flex-auto flex flex-col">
          {children}
        </main>
      </div>
    </QueryClientProvider>
  );
}
