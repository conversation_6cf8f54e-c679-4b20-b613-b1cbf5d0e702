import { useState } from "react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { FormValues } from "../[[...path]]/containers/edit-content-page";
import { UseFormReturn } from "react-hook-form";

export const SeoSuggestButton = ({
  form,
  title,
  content,
  language,
}: {
  form: UseFormReturn<FormValues>;
  title: string;
  content: string;
  language: string;
}) => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const getSuggestion = async () => {
    setIsLoading(true);
    try {
      const res = await fetch("/api/assistance/seo", {
        method: "POST",
        body: JSON.stringify({
          title,
          article: content,
          lang: language,
        }),
      });
      const data = await res.json();

      const fieldConfig: {
        label: string;
        formFieldKey: keyof FormValues;
        dataKey: keyof typeof data;
      }[] = [
        {
          label: "URL slug",
          formFieldKey: "slug",
          dataKey: "slug",
        },
        {
          label: "Title",
          formFieldKey: "title",
          dataKey: "title",
        },
        {
          label: "Description",
          formFieldKey: "seoDescription",
          dataKey: "description",
        },
      ];

      // Show suggestions in toast
      const suggestions = fieldConfig
        .map(({ label, dataKey }) => `${label}: ${data[dataKey]}`)
        .join("\n\n");

      toast({
        title: "SEO Suggestions",
        description: suggestions,
        duration: 10000,
      });

      // Auto-apply suggestions to form
      fieldConfig.forEach(({ formFieldKey, dataKey }) => {
        form.setValue(formFieldKey, data[dataKey]);
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button disabled={isLoading} onClick={getSuggestion}>
      {isLoading ? "Loading..." : "SEO suggestions"}
    </Button>
  );
};
